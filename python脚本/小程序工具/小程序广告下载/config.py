#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

# 数据库配置
DATABASE_CONFIG = {
    'db_path': 'weapp_ads.db'
}

# API配置
API_CONFIG = {
    'base_url': 'http://game.raisedsun.com/prod-api/weapp-ad-info/query-all',
    'headers': {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImEwNTUzOTk4LTY0NzctNGQ4Yi04ZTc1LWQ1NTMzOTc0OGFlNyJ9.BcaeJuNmbxwBf5E3DglJ-jaFg-CuUj6_nRXToHQ7DRlAcXdsBIz9JbVufaqolKmVqXN07pOhTKgfoU09Fw2J9w',
        'Proxy-Connection': 'keep-alive',
        'Referer': 'http://game.raisedsun.com/weapp/ad/index',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'
    },
    'cookies': {
        'Admin-Token': 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImEwNTUzOTk4LTY0NzctNGQ4Yi04ZTc1LWQ1NTMzOTc0OGFlNyJ9.BcaeJuNmbxwBf5E3DglJ-jaFg-CuUj6_nRXToHQ7DRlAcXdsBIz9JbVufaqolKmVqXN07pOhTKgfoU09Fw2J9w'
    }
}

# 爬取配置
CRAWL_CONFIG = {
    'meet_count_threshold': 10,  # meetCount阈值，小于等于此值时停止爬取
    'page_size': 50,            # 每页记录数
    'delay_between_requests': 1, # 请求间隔（秒）
    'request_timeout': 30,      # 请求超时时间（秒）
    'max_retries': 3           # 最大重试次数
}

# 默认请求参数
DEFAULT_PARAMS = {
    'last_appear_time_gte': 1751299200000,  # 2025-07-01 00:00:00
    'last_appear_time_lte': 1753891199999,  # 2025-07-31 23:59:59
    'ad_type': 5
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file': 'weapp_crawler.log'
}
