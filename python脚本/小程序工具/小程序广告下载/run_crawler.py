#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主运行脚本
提供命令行界面来运行爬虫和管理数据库
"""

import argparse
import sys
import json
from datetime import datetime, timedelta
from 获取广告数据 import WeappAdCrawler
from database_manager import DatabaseManager
from config import DEFAULT_PARAMS, CRAWL_CONFIG

def timestamp_to_date(timestamp):
    """将时间戳转换为日期字符串"""
    return datetime.fromtimestamp(timestamp / 1000).strftime('%Y-%m-%d %H:%M:%S')

def date_to_timestamp(date_str):
    """将日期字符串转换为时间戳"""
    dt = datetime.strptime(date_str, '%Y-%m-%d')
    return int(dt.timestamp() * 1000)

def run_crawler(args):
    """运行爬虫"""
    print("=== 小程序广告爬虫 ===")
    
    # 构建请求参数
    params = DEFAULT_PARAMS.copy()
    
    if args.start_date:
        params['last_appear_time_gte'] = date_to_timestamp(args.start_date)
    
    if args.end_date:
        # 结束日期设为当天23:59:59
        end_dt = datetime.strptime(args.end_date, '%Y-%m-%d')
        end_dt = end_dt.replace(hour=23, minute=59, second=59)
        params['last_appear_time_lte'] = int(end_dt.timestamp() * 1000)
    
    if args.ad_type:
        params['ad_type'] = args.ad_type
    
    print(f"爬取参数:")
    print(f"  时间范围: {timestamp_to_date(params['last_appear_time_gte'])} ~ {timestamp_to_date(params['last_appear_time_lte'])}")
    print(f"  广告类型: {params['ad_type']}")
    print(f"  MeetCount阈值: {CRAWL_CONFIG['meet_count_threshold']}")
    print()
    
    # 创建爬虫并运行
    crawler = WeappAdCrawler(args.database)
    
    # 如果指定了阈值，更新配置
    if args.threshold:
        crawler.meet_count_threshold = args.threshold
        print(f"使用自定义阈值: {args.threshold}")
    
    result = crawler.run(**params)
    
    if result:
        print(f"\n爬取完成!")
        print(f"总页数: {result['total_pages']}")
        print(f"总记录数: {result['total_records']}")
        print(f"完成时间: {result['completed_at']}")

def show_statistics(args):
    """显示数据库统计信息"""
    print("=== 数据库统计信息 ===")
    
    db_manager = DatabaseManager(args.database)
    stats = db_manager.get_statistics()
    
    if not stats:
        print("获取统计信息失败")
        return
    
    print(f"总广告数: {stats['total_ads']}")
    print(f"MeetCount统计:")
    print(f"  最小值: {stats['meet_count_stats']['min']}")
    print(f"  最大值: {stats['meet_count_stats']['max']}")
    print(f"  平均值: {stats['meet_count_stats']['avg']}")
    
    print(f"\n广告类型分布:")
    for ad_type, count in stats['ad_type_stats']:
        print(f"  类型 {ad_type}: {count} 条")
    
    print(f"\n最近爬取记录:")
    for record in stats['recent_crawls']:
        crawl_time = record[6] if len(record) > 6 else "未知"
        status = record[5] if len(record) > 5 else "未知"
        print(f"  页码 {record[1]}: {record[2]} 条记录, 状态: {status}, 时间: {crawl_time}")

def export_data(args):
    """导出数据"""
    print("=== 导出数据 ===")
    
    db_manager = DatabaseManager(args.database)
    
    threshold = args.threshold if args.threshold else 10
    output_file = args.output if args.output else f"weapp_ads_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    print(f"导出条件: meetCount >= {threshold}")
    print(f"输出文件: {output_file}")
    
    success = db_manager.export_data(output_file, threshold)
    
    if success:
        print("导出成功!")
    else:
        print("导出失败!")

def main():
    parser = argparse.ArgumentParser(description='小程序广告爬虫工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 通用参数
    parser.add_argument('--database', '-d', default='weapp_ads.db', help='数据库文件路径')
    
    # 爬取命令
    crawl_parser = subparsers.add_parser('crawl', help='运行爬虫')
    crawl_parser.add_argument('--start-date', help='开始日期 (YYYY-MM-DD)')
    crawl_parser.add_argument('--end-date', help='结束日期 (YYYY-MM-DD)')
    crawl_parser.add_argument('--ad-type', type=int, help='广告类型')
    crawl_parser.add_argument('--threshold', type=int, help='MeetCount阈值')
    
    # 统计命令
    stats_parser = subparsers.add_parser('stats', help='显示统计信息')
    
    # 导出命令
    export_parser = subparsers.add_parser('export', help='导出数据')
    export_parser.add_argument('--threshold', type=int, help='MeetCount阈值')
    export_parser.add_argument('--output', '-o', help='输出文件名')
    
    # 初始化数据库命令
    init_parser = subparsers.add_parser('init', help='初始化数据库')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == 'crawl':
            run_crawler(args)
        elif args.command == 'stats':
            show_statistics(args)
        elif args.command == 'export':
            export_data(args)
        elif args.command == 'init':
            print("初始化数据库...")
            DatabaseManager(args.database)
            print("数据库初始化完成!")
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
