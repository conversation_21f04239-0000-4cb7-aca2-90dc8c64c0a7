#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理脚本
用于创建和管理小程序广告数据库
"""

import sqlite3
import os
from datetime import datetime
import json

class DatabaseManager:
    def __init__(self, db_path="weapp_ads.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path (str): 数据库文件路径
        """
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库，创建表结构"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 创建广告信息表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS weapp_ads (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ad_id TEXT UNIQUE,
                app_name TEXT,
                app_id TEXT,
                ad_type INTEGER,
                meet_count INTEGER,
                last_appear_time INTEGER,
                first_appear_time INTEGER,
                image_url TEXT,
                title TEXT,
                ad_content TEXT,
                ad_images TEXT,
                ad_videos TEXT,
                landing_page TEXT,
                advertiser_info TEXT,
                raw_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建爬取记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS crawl_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                page_no INTEGER,
                total_records INTEGER,
                min_meet_count INTEGER,
                max_meet_count INTEGER,
                crawl_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT,
                error_message TEXT
            )
        ''')

        # 升级数据库结构（添加新字段）
        self._upgrade_database(cursor)

        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_meet_count ON weapp_ads(meet_count)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_last_appear_time ON weapp_ads(last_appear_time)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_ad_type ON weapp_ads(ad_type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_title ON weapp_ads(title)')

        conn.commit()
        conn.close()
        print(f"数据库初始化完成: {self.db_path}")

    def _upgrade_database(self, cursor):
        """升级数据库结构，添加新字段"""
        try:
            # 检查是否需要添加image_url字段
            cursor.execute("PRAGMA table_info(weapp_ads)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'image_url' not in columns:
                cursor.execute('ALTER TABLE weapp_ads ADD COLUMN image_url TEXT')
                print("添加image_url字段")

            if 'title' not in columns:
                cursor.execute('ALTER TABLE weapp_ads ADD COLUMN title TEXT')
                print("添加title字段")

        except Exception as e:
            print(f"数据库升级失败: {e}")
    
    def insert_ad_data(self, ad_data):
        """
        插入或更新广告数据
        根据ad_id判断是新增还是更新

        Args:
            ad_data (dict): 广告数据

        Returns:
            tuple: (是否成功, 操作类型: 'insert'/'update')
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 提取主要字段
            ad_id = ad_data.get('id', '')
            app_name = ad_data.get('appName', '') or ad_data.get('mpName', '')
            app_id = ad_data.get('appId', '') or ad_data.get('targetAppId', '')
            ad_type = ad_data.get('adType', 0)
            meet_count = ad_data.get('meetCount', 0)
            last_appear_time = ad_data.get('lastAppearTime', 0)
            first_appear_time = ad_data.get('firstAppearTime', 0)

            # 新增字段
            image_url = ad_data.get('imageUrl', '')
            title = ad_data.get('title', '')

            # 处理复杂字段
            ad_content = json.dumps(ad_data.get('adContent', {}), ensure_ascii=False)
            ad_images = json.dumps(ad_data.get('adImages', []), ensure_ascii=False)
            ad_videos = json.dumps(ad_data.get('adVideos', []), ensure_ascii=False)
            landing_page = ad_data.get('landingPage', '') or ad_data.get('jumpUrl', '')
            advertiser_info = json.dumps(ad_data.get('advertiserInfo', {}), ensure_ascii=False)
            raw_data = json.dumps(ad_data, ensure_ascii=False)

            # 检查记录是否已存在
            cursor.execute('SELECT id, meet_count, created_at FROM weapp_ads WHERE ad_id = ?', (ad_id,))
            existing_record = cursor.fetchone()

            if existing_record:
                # 更新现有记录
                existing_id, existing_meet_count, created_at = existing_record

                cursor.execute('''
                    UPDATE weapp_ads SET
                        app_name = ?, app_id = ?, ad_type = ?, meet_count = ?,
                        last_appear_time = ?, first_appear_time = ?, image_url = ?, title = ?,
                        ad_content = ?, ad_images = ?, ad_videos = ?, landing_page = ?,
                        advertiser_info = ?, raw_data = ?, updated_at = ?
                    WHERE ad_id = ?
                ''', (
                    app_name, app_id, ad_type, meet_count,
                    last_appear_time, first_appear_time, image_url, title,
                    ad_content, ad_images, ad_videos, landing_page,
                    advertiser_info, raw_data, datetime.now(), ad_id
                ))

                operation_type = 'update'

                # 如果meetCount有变化，记录日志
                if existing_meet_count != meet_count:
                    print(f"更新广告 {ad_id}: meetCount {existing_meet_count} -> {meet_count}")

            else:
                # 插入新记录
                cursor.execute('''
                    INSERT INTO weapp_ads (
                        ad_id, app_name, app_id, ad_type, meet_count,
                        last_appear_time, first_appear_time, image_url, title,
                        ad_content, ad_images, ad_videos, landing_page,
                        advertiser_info, raw_data, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    ad_id, app_name, app_id, ad_type, meet_count,
                    last_appear_time, first_appear_time, image_url, title,
                    ad_content, ad_images, ad_videos, landing_page,
                    advertiser_info, raw_data, datetime.now(), datetime.now()
                ))

                operation_type = 'insert'
                print(f"新增广告 {ad_id}: {title} (meetCount: {meet_count})")

            conn.commit()
            return True, operation_type

        except Exception as e:
            print(f"处理数据失败: {e}")
            print(f"广告ID: {ad_data.get('id', 'N/A')}")
            conn.rollback()
            return False, 'error'
        finally:
            conn.close()
    
    def log_crawl_record(self, page_no, total_records, min_meet_count, max_meet_count, status, error_message=None):
        """
        记录爬取日志
        
        Args:
            page_no (int): 页码
            total_records (int): 记录总数
            min_meet_count (int): 最小meetCount
            max_meet_count (int): 最大meetCount
            status (str): 状态
            error_message (str): 错误信息
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO crawl_logs (
                    page_no, total_records, min_meet_count, max_meet_count, 
                    status, error_message
                ) VALUES (?, ?, ?, ?, ?, ?)
            ''', (page_no, total_records, min_meet_count, max_meet_count, status, error_message))
            
            conn.commit()
            
        except Exception as e:
            print(f"记录日志失败: {e}")
        finally:
            conn.close()
    
    def get_statistics(self):
        """获取数据库统计信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 总记录数
            cursor.execute('SELECT COUNT(*) FROM weapp_ads')
            total_ads = cursor.fetchone()[0]
            
            # meetCount统计
            cursor.execute('SELECT MIN(meet_count), MAX(meet_count), AVG(meet_count) FROM weapp_ads')
            min_meet, max_meet, avg_meet = cursor.fetchone()
            
            # 按adType分组统计
            cursor.execute('SELECT ad_type, COUNT(*) FROM weapp_ads GROUP BY ad_type')
            ad_type_stats = cursor.fetchall()
            
            # 最近爬取记录
            cursor.execute('SELECT * FROM crawl_logs ORDER BY crawl_time DESC LIMIT 5')
            recent_crawls = cursor.fetchall()
            
            return {
                'total_ads': total_ads,
                'meet_count_stats': {
                    'min': min_meet,
                    'max': max_meet,
                    'avg': round(avg_meet, 2) if avg_meet else 0
                },
                'ad_type_stats': ad_type_stats,
                'recent_crawls': recent_crawls
            }
            
        except Exception as e:
            print(f"获取统计信息失败: {e}")
            return None
        finally:
            conn.close()
    
    def export_data(self, output_file="weapp_ads_export.json", meet_count_threshold=10):
        """
        导出数据到JSON文件
        
        Args:
            output_file (str): 输出文件名
            meet_count_threshold (int): meetCount阈值
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                SELECT * FROM weapp_ads 
                WHERE meet_count >= ? 
                ORDER BY meet_count DESC
            ''', (meet_count_threshold,))
            
            rows = cursor.fetchall()
            columns = [description[0] for description in cursor.description]
            
            data = []
            for row in rows:
                data.append(dict(zip(columns, row)))
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"数据导出完成: {output_file}, 共 {len(data)} 条记录")
            return True
            
        except Exception as e:
            print(f"导出数据失败: {e}")
            return False
        finally:
            conn.close()

if __name__ == "__main__":
    # 测试数据库管理器
    db_manager = DatabaseManager()
    
    # 显示统计信息
    stats = db_manager.get_statistics()
    if stats:
        print("\n=== 数据库统计信息 ===")
        print(f"总广告数: {stats['total_ads']}")
        print(f"MeetCount统计: 最小={stats['meet_count_stats']['min']}, "
              f"最大={stats['meet_count_stats']['max']}, "
              f"平均={stats['meet_count_stats']['avg']}")
        print(f"广告类型分布: {stats['ad_type_stats']}")
        print(f"最近爬取记录数: {len(stats['recent_crawls'])}")
