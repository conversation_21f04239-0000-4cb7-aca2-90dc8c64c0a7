#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
广告图片下载器
从数据库中读取广告数据，下载图片到本地文件夹
使用时间戳作为文件名后缀，避免重复下载
"""

import os
import sqlite3
import requests
import time
import logging
from datetime import datetime
from urllib.parse import urlparse
import hashlib
import re

class AdImageDownloader:
    def __init__(self, db_path="weapp_ads.db", download_dir="广告图片"):
        """
        初始化图片下载器
        
        Args:
            db_path (str): 数据库文件路径
            download_dir (str): 下载目录
        """
        self.db_path = db_path
        self.download_dir = download_dir
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
        
        # 设置日志
        self.setup_logging()
        
        # 创建下载目录
        self.create_directories()
        
        # 初始化下载记录表
        self.init_download_records()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('image_downloader.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def create_directories(self):
        """创建下载目录"""
        if not os.path.exists(self.download_dir):
            os.makedirs(self.download_dir)
            self.logger.info(f"创建下载目录: {self.download_dir}")
    
    def init_download_records(self):
        """初始化下载记录表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS download_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ad_id TEXT,
                    image_url TEXT,
                    local_filename TEXT,
                    download_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    file_size INTEGER,
                    status TEXT,
                    UNIQUE(ad_id, image_url)
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_download_ad_id ON download_records(ad_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_download_url ON download_records(image_url)')
            
            conn.commit()
            self.logger.info("下载记录表初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化下载记录表失败: {e}")
        finally:
            conn.close()
    
    def sanitize_filename(self, filename):
        """清理文件名，移除非法字符"""
        # 移除或替换非法字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 限制长度
        if len(filename) > 100:
            filename = filename[:100]
        return filename
    
    def get_file_extension(self, url, content_type=None):
        """获取文件扩展名"""
        # 先从URL路径获取
        parsed_url = urlparse(url)
        path = parsed_url.path
        if '.' in path:
            ext = os.path.splitext(path)[1].lower()
            if ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']:
                return ext
        
        # 从Content-Type获取
        if content_type:
            if 'jpeg' in content_type or 'jpg' in content_type:
                return '.jpg'
            elif 'png' in content_type:
                return '.png'
            elif 'gif' in content_type:
                return '.gif'
            elif 'webp' in content_type:
                return '.webp'
        
        # 默认使用jpg
        return '.jpg'
    
    def is_already_downloaded(self, ad_id, image_url):
        """检查是否已经下载过"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                SELECT local_filename, status FROM download_records 
                WHERE ad_id = ? AND image_url = ? AND status = 'success'
            ''', (ad_id, image_url))
            
            result = cursor.fetchone()
            if result:
                local_filename, status = result
                # 检查文件是否还存在
                full_path = os.path.join(self.download_dir, local_filename)
                if os.path.exists(full_path):
                    return True, local_filename
                else:
                    # 文件不存在，删除记录
                    cursor.execute('''
                        DELETE FROM download_records 
                        WHERE ad_id = ? AND image_url = ?
                    ''', (ad_id, image_url))
                    conn.commit()
            
            return False, None
            
        except Exception as e:
            self.logger.error(f"检查下载记录失败: {e}")
            return False, None
        finally:
            conn.close()
    
    def record_download(self, ad_id, image_url, local_filename, file_size, status):
        """记录下载结果"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT OR REPLACE INTO download_records 
                (ad_id, image_url, local_filename, file_size, status, download_time)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (ad_id, image_url, local_filename, file_size, status, datetime.now()))
            
            conn.commit()
            
        except Exception as e:
            self.logger.error(f"记录下载结果失败: {e}")
        finally:
            conn.close()
    
    def download_image(self, ad_id, image_url, title):
        """
        下载单个图片
        
        Args:
            ad_id (str): 广告ID
            image_url (str): 图片URL
            title (str): 广告标题
            
        Returns:
            tuple: (是否成功, 本地文件名)
        """
        try:
            # 检查是否已下载
            already_downloaded, existing_filename = self.is_already_downloaded(ad_id, image_url)
            if already_downloaded:
                self.logger.info(f"图片已存在，跳过下载: {existing_filename}")
                return True, existing_filename
            
            # 下载图片
            self.logger.info(f"开始下载: {title} - {image_url}")
            
            response = self.session.get(image_url, timeout=30, stream=True)
            response.raise_for_status()
            
            # 获取文件扩展名
            content_type = response.headers.get('content-type', '')
            file_ext = self.get_file_extension(image_url, content_type)
            
            # 生成文件名：标题_广告ID_时间戳.扩展名
            timestamp = int(time.time())
            safe_title = self.sanitize_filename(title) if title else "无标题"
            filename = f"{safe_title}_{ad_id[:8]}_{timestamp}{file_ext}"
            
            # 保存文件
            full_path = os.path.join(self.download_dir, filename)
            file_size = 0
            
            with open(full_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        file_size += len(chunk)
            
            # 记录下载成功
            self.record_download(ad_id, image_url, filename, file_size, 'success')
            
            self.logger.info(f"下载成功: {filename} ({file_size} bytes)")
            return True, filename
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"下载失败 {image_url}: {e}")
            self.record_download(ad_id, image_url, '', 0, f'error: {str(e)}')
            return False, None
        except Exception as e:
            self.logger.error(f"下载异常 {image_url}: {e}")
            self.record_download(ad_id, image_url, '', 0, f'error: {str(e)}')
            return False, None
    
    def get_ads_with_images(self, limit=None, min_meet_count=None):
        """
        获取有图片URL的广告数据
        
        Args:
            limit (int): 限制数量
            min_meet_count (int): 最小meetCount
            
        Returns:
            list: 广告数据列表
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            sql = '''
                SELECT ad_id, title, image_url, meet_count 
                FROM weapp_ads 
                WHERE image_url IS NOT NULL AND image_url != ''
            '''
            params = []
            
            if min_meet_count:
                sql += ' AND meet_count >= ?'
                params.append(min_meet_count)
            
            sql += ' ORDER BY meet_count DESC'
            
            if limit:
                sql += ' LIMIT ?'
                params.append(limit)
            
            cursor.execute(sql, params)
            return cursor.fetchall()
            
        except Exception as e:
            self.logger.error(f"查询广告数据失败: {e}")
            return []
        finally:
            conn.close()
    
    def download_all_images(self, limit=None, min_meet_count=None):
        """
        下载所有图片
        
        Args:
            limit (int): 限制下载数量
            min_meet_count (int): 最小meetCount阈值
        """
        self.logger.info("开始批量下载广告图片...")
        
        # 获取广告数据
        ads = self.get_ads_with_images(limit, min_meet_count)
        
        if not ads:
            self.logger.info("没有找到需要下载的图片")
            return
        
        self.logger.info(f"找到 {len(ads)} 个广告图片需要处理")
        
        success_count = 0
        skip_count = 0
        error_count = 0
        
        for i, (ad_id, title, image_url, meet_count) in enumerate(ads, 1):
            self.logger.info(f"处理进度: {i}/{len(ads)} - MeetCount: {meet_count}")
            
            success, filename = self.download_image(ad_id, image_url, title)
            
            if success:
                if filename:
                    success_count += 1
                else:
                    skip_count += 1
            else:
                error_count += 1
            
            # 添加延迟避免请求过快
            time.sleep(0.1)
        
        self.logger.info(f"下载完成! 成功: {success_count}, 跳过: {skip_count}, 失败: {error_count}")
    
    def get_download_stats(self):
        """获取下载统计信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 总下载记录
            cursor.execute('SELECT COUNT(*) FROM download_records')
            total_records = cursor.fetchone()[0]
            
            # 成功下载
            cursor.execute("SELECT COUNT(*) FROM download_records WHERE status = 'success'")
            success_count = cursor.fetchone()[0]
            
            # 失败下载
            cursor.execute("SELECT COUNT(*) FROM download_records WHERE status LIKE 'error%'")
            error_count = cursor.fetchone()[0]
            
            # 总文件大小
            cursor.execute("SELECT SUM(file_size) FROM download_records WHERE status = 'success'")
            total_size = cursor.fetchone()[0] or 0
            
            return {
                'total_records': total_records,
                'success_count': success_count,
                'error_count': error_count,
                'total_size': total_size
            }
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}
        finally:
            conn.close()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='广告图片下载器')
    parser.add_argument('--limit', type=int, help='限制下载数量')
    parser.add_argument('--min-meet-count', type=int, default=1000, help='最小meetCount阈值 (默认: 1000)')
    parser.add_argument('--download-dir', default='广告图片', help='下载目录 (默认: 广告图片)')
    parser.add_argument('--stats', action='store_true', help='显示下载统计信息')
    
    args = parser.parse_args()
    
    downloader = AdImageDownloader(download_dir=args.download_dir)
    
    if args.stats:
        stats = downloader.get_download_stats()
        print("=== 下载统计信息 ===")
        print(f"总下载记录: {stats.get('total_records', 0)}")
        print(f"成功下载: {stats.get('success_count', 0)}")
        print(f"下载失败: {stats.get('error_count', 0)}")
        print(f"总文件大小: {stats.get('total_size', 0) / 1024 / 1024:.2f} MB")
    else:
        downloader.download_all_images(
            limit=args.limit,
            min_meet_count=args.min_meet_count
        )
